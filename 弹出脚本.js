// 弹窗杀手 - 现代化弹出页面脚本
class PopupController {
    constructor() {
        this.elements = {
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            closedCount: document.getElementById('closedCount'),
            activeTabs: document.getElementById('activeTabs'),
            toggleBtn: document.getElementById('toggleBtn'),
            toggleText: document.getElementById('toggleText'),
            btnIcon: document.getElementById('btnIcon'),
            refreshBtn: document.getElementById('refreshBtn'),
            clearBtn: document.getElementById('clearBtn'),
            settingsBtn: document.getElementById('settingsBtn'),
            settingsPanel: document.getElementById('settingsPanel'),
            closeSettings: document.getElementById('closeSettings'),
            autoStart: document.getElementById('autoStart'),
            showLogs: document.getElementById('showLogs')
        };

        this.currentStatus = {
            enabled: false,
            closedCount: 0,
            totalTabs: 0,
            enabledTabs: 0
        };

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadSettings();
        this.updateStatus();

        // 定期更新状态
        setInterval(() => {
            this.updateStatus();
        }, 1000);
    }

    bindEvents() {
        // 主切换按钮
        this.elements.toggleBtn.addEventListener('click', () => {
            this.toggleGlobal();
        });

        // 刷新按钮
        this.elements.refreshBtn.addEventListener('click', () => {
            this.updateStatus();
            this.showFeedback('已刷新');
        });

        // 清零按钮
        this.elements.clearBtn.addEventListener('click', () => {
            this.clearCount();
        });

        // 设置按钮
        this.elements.settingsBtn.addEventListener('click', () => {
            this.showSettings();
        });

        // 关闭设置
        this.elements.closeSettings.addEventListener('click', () => {
            this.hideSettings();
        });

        // 设置变更
        this.elements.autoStart.addEventListener('change', () => {
            this.saveSettings();
        });

        this.elements.showLogs.addEventListener('change', () => {
            this.saveSettings();
        });
    }
    
    async updateStatus() {
        try {
            // 获取当前标签页状态
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            if (tab) {
                const response = await chrome.tabs.sendMessage(tab.id, {action: 'getStatus'});
                if (response) {
                    this.currentStatus = {
                        enabled: response.enabled,
                        closedCount: response.closedCount,
                        totalTabs: 1,
                        enabledTabs: response.enabled ? 1 : 0
                    };
                    this.updateUI();
                    return;
                }
            }

            // 如果无法获取当前标签页状态，获取全局状态
            const globalResponse = await chrome.runtime.sendMessage({
                action: 'getGlobalStatus'
            });

            if (globalResponse && !globalResponse.error) {
                this.currentStatus = globalResponse;
                this.updateUI();
            } else {
                this.showError('获取状态失败');
            }
        } catch (error) {
            console.error('更新状态时出错:', error);
            this.showError('连接失败');
        }
    }

    updateUI() {
        const { enabled, closedCount, totalTabs, enabledTabs, globalEnabled } = this.currentStatus;
        const isActive = enabled || globalEnabled;

        // 更新状态指示器
        const pulseDot = this.elements.statusIndicator.querySelector('.pulse-dot');
        if (isActive) {
            pulseDot.classList.remove('inactive');
            this.elements.statusText.textContent = '拦截中...';
            this.elements.toggleBtn.classList.remove('disabled');
            this.elements.toggleText.textContent = '停止拦截';
            this.elements.btnIcon.textContent = '🛡️';
        } else {
            pulseDot.classList.add('inactive');
            this.elements.statusText.textContent = '已停止';
            this.elements.toggleBtn.classList.add('disabled');
            this.elements.toggleText.textContent = '启动拦截';
            this.elements.btnIcon.textContent = '⚡';
        }

        // 更新计数（添加动画效果）
        const newCount = closedCount || 0;
        if (newCount !== parseInt(this.elements.closedCount.textContent)) {
            this.elements.closedCount.textContent = newCount;
            this.elements.closedCount.classList.add('count-animation');
            setTimeout(() => {
                this.elements.closedCount.classList.remove('count-animation');
            }, 500);
        }

        this.elements.activeTabs.textContent = enabledTabs || 0;
    }
    
    async toggleGlobal() {
        try {
            this.elements.toggleBtn.disabled = true;
            this.elements.toggleText.textContent = '切换中...';

            // 尝试切换当前标签页
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            if (tab) {
                try {
                    const response = await chrome.tabs.sendMessage(tab.id, {action: 'toggle'});
                    if (response) {
                        setTimeout(() => {
                            this.updateStatus();
                        }, 300);
                        this.showFeedback(response.enabled ? '已启动' : '已停止');
                        return;
                    }
                } catch (error) {
                    console.log('当前标签页无法切换，尝试全局切换');
                }
            }

            // 如果当前标签页切换失败，尝试全局切换
            const response = await chrome.runtime.sendMessage({
                action: 'toggleGlobal'
            });

            if (response && response.success) {
                setTimeout(() => {
                    this.updateStatus();
                }, 500);
                this.showFeedback('已切换');
            } else {
                this.showError('切换失败');
            }
        } catch (error) {
            console.error('切换状态时出错:', error);
            this.showError('操作失败');
        } finally {
            this.elements.toggleBtn.disabled = false;
        }
    }
    
    async clearCount() {
        try {
            // 清零当前标签页计数
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            if (tab) {
                await chrome.tabs.sendMessage(tab.id, {action: 'clearCount'});
            }

            this.elements.closedCount.textContent = '0';
            this.showFeedback('已清零');
        } catch (error) {
            console.error('清零失败:', error);
        }
    }

    showSettings() {
        this.elements.settingsPanel.classList.add('show');
    }

    hideSettings() {
        this.elements.settingsPanel.classList.remove('show');
    }

    loadSettings() {
        chrome.storage.sync.get(['autoStart', 'showLogs'], (result) => {
            this.elements.autoStart.checked = result.autoStart !== false;
            this.elements.showLogs.checked = result.showLogs === true;
        });
    }

    saveSettings() {
        const settings = {
            autoStart: this.elements.autoStart.checked,
            showLogs: this.elements.showLogs.checked
        };

        chrome.storage.sync.set(settings, () => {
            console.log('设置已保存:', settings);
            this.showFeedback('设置已保存');
        });
    }

    showError(message) {
        this.elements.statusText.textContent = message;
        const pulseDot = this.elements.statusIndicator.querySelector('.pulse-dot');
        pulseDot.classList.add('inactive');

        setTimeout(() => {
            this.updateStatus();
        }, 3000);
    }

    showFeedback(message) {
        const originalText = this.elements.statusText.textContent;
        this.elements.statusText.textContent = message;

        setTimeout(() => {
            this.elements.statusText.textContent = originalText;
        }, 1500);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});

// 添加一些实用功能
document.addEventListener('keydown', (event) => {
    // 按 Escape 键关闭弹出窗口
    if (event.key === 'Escape') {
        window.close();
    }
    
    // 按 F5 或 Ctrl+R 刷新状态
    if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
        event.preventDefault();
        document.getElementById('refreshBtn').click();
    }
});
