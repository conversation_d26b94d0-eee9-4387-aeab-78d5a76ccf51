// 广告弹窗杀手 - 弹出页面脚本
class PopupController {
    constructor() {
        this.elements = {
            status: document.getElementById('status'),
            closedCount: document.getElementById('closedCount'),
            activeTabs: document.getElementById('activeTabs'),
            toggleBtn: document.getElementById('toggleBtn'),
            toggleText: document.getElementById('toggleText'),
            refreshBtn: document.getElementById('refreshBtn'),
            autoStart: document.getElementById('autoStart'),
            showNotifications: document.getElementById('showNotifications')
        };
        
        this.currentStatus = {
            enabled: false,
            closedCount: 0,
            totalTabs: 0,
            enabledTabs: 0
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadSettings();
        this.updateStatus();
        
        // 定期更新状态
        setInterval(() => {
            this.updateStatus();
        }, 2000);
    }
    
    bindEvents() {
        // 切换按钮
        this.elements.toggleBtn.addEventListener('click', () => {
            this.toggleGlobal();
        });
        
        // 刷新按钮
        this.elements.refreshBtn.addEventListener('click', () => {
            this.updateStatus();
        });
        
        // 设置变更
        this.elements.autoStart.addEventListener('change', () => {
            this.saveSettings();
        });
        
        this.elements.showNotifications.addEventListener('change', () => {
            this.saveSettings();
        });
    }
    
    async updateStatus() {
        try {
            // 获取全局状态
            const response = await chrome.runtime.sendMessage({
                action: 'getGlobalStatus'
            });
            
            if (response && !response.error) {
                this.currentStatus = response;
                this.updateUI();
            } else {
                this.showError('获取状态失败');
            }
        } catch (error) {
            console.error('更新状态时出错:', error);
            this.showError('连接失败');
        }
    }
    
    updateUI() {
        const { enabled, closedCount, totalTabs, enabledTabs, globalEnabled } = this.currentStatus;
        
        // 更新状态显示
        if (globalEnabled) {
            this.elements.status.innerHTML = `
                <span class="status-indicator active"></span>
                已启用
            `;
            this.elements.toggleBtn.classList.remove('disabled');
            this.elements.toggleText.textContent = '禁用';
        } else {
            this.elements.status.innerHTML = `
                <span class="status-indicator inactive"></span>
                已禁用
            `;
            this.elements.toggleBtn.classList.add('disabled');
            this.elements.toggleText.textContent = '启用';
        }
        
        // 更新计数
        this.elements.closedCount.textContent = closedCount || 0;
        this.elements.activeTabs.textContent = `${enabledTabs}/${totalTabs}`;
        
        // 添加脉冲效果（如果有活动）
        if (closedCount > 0) {
            this.elements.closedCount.classList.add('pulse');
            setTimeout(() => {
                this.elements.closedCount.classList.remove('pulse');
            }, 2000);
        }
    }
    
    async toggleGlobal() {
        try {
            this.elements.toggleBtn.disabled = true;
            this.elements.toggleText.textContent = '切换中...';
            
            const response = await chrome.runtime.sendMessage({
                action: 'toggleGlobal'
            });
            
            if (response && response.success) {
                // 延迟更新状态，让切换操作完成
                setTimeout(() => {
                    this.updateStatus();
                }, 500);
            } else {
                this.showError('切换失败');
            }
        } catch (error) {
            console.error('切换状态时出错:', error);
            this.showError('操作失败');
        } finally {
            this.elements.toggleBtn.disabled = false;
        }
    }
    
    loadSettings() {
        // 从存储中加载设置
        chrome.storage.sync.get(['autoStart', 'showNotifications'], (result) => {
            this.elements.autoStart.checked = result.autoStart !== false; // 默认启用
            this.elements.showNotifications.checked = result.showNotifications === true; // 默认禁用
        });
    }
    
    saveSettings() {
        // 保存设置到存储
        const settings = {
            autoStart: this.elements.autoStart.checked,
            showNotifications: this.elements.showNotifications.checked
        };
        
        chrome.storage.sync.set(settings, () => {
            console.log('设置已保存:', settings);
        });
    }
    
    showError(message) {
        this.elements.status.innerHTML = `
            <span class="status-indicator inactive"></span>
            ${message}
        `;
        
        // 3秒后恢复正常显示
        setTimeout(() => {
            this.updateStatus();
        }, 3000);
    }
    
    showSuccess(message) {
        const originalText = this.elements.status.innerHTML;
        this.elements.status.innerHTML = `
            <span class="status-indicator active"></span>
            ${message}
        `;
        
        // 2秒后恢复正常显示
        setTimeout(() => {
            this.elements.status.innerHTML = originalText;
        }, 2000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});

// 添加一些实用功能
document.addEventListener('keydown', (event) => {
    // 按 Escape 键关闭弹出窗口
    if (event.key === 'Escape') {
        window.close();
    }
    
    // 按 F5 或 Ctrl+R 刷新状态
    if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
        event.preventDefault();
        document.getElementById('refreshBtn').click();
    }
});
