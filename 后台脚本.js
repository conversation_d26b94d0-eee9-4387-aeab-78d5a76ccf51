// 广告弹窗杀手 - 后台脚本
class BackgroundService {
    constructor() {
        this.init();
    }
    
    init() {
        console.log('广告弹窗杀手后台服务已启动');
        
        // 监听插件安装
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('广告弹窗杀手插件已安装');
                this.showWelcomeNotification();
            }
        });
        
        // 监听来自弹出页面的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });
        
        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.injectContentScript(tabId);
            }
        });
    }
    
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'toggleGlobal':
                    await this.toggleAllTabs();
                    sendResponse({success: true});
                    break;
                    
                case 'getGlobalStatus':
                    const status = await this.getGlobalStatus();
                    sendResponse(status);
                    break;
                    
                case 'getCurrentTabStatus':
                    if (sender.tab) {
                        const tabStatus = await this.getTabStatus(sender.tab.id);
                        sendResponse(tabStatus);
                    }
                    break;
                    
                default:
                    sendResponse({error: '未知操作'});
            }
        } catch (error) {
            console.error('处理消息时出错:', error);
            sendResponse({error: error.message});
        }
    }
    
    async injectContentScript(tabId) {
        try {
            // 检查是否可以注入脚本
            const tab = await chrome.tabs.get(tabId);
            if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                return;
            }
            
            // 注入内容脚本（如果还没有注入）
            await chrome.scripting.executeScript({
                target: {tabId: tabId},
                files: ['内容脚本.js']
            });
            
        } catch (error) {
            // 忽略注入失败的情况（可能是权限问题）
            console.log('无法注入内容脚本到标签页:', tabId, error.message);
        }
    }
    
    async toggleAllTabs() {
        try {
            const tabs = await chrome.tabs.query({});
            
            for (const tab of tabs) {
                if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                    try {
                        await chrome.tabs.sendMessage(tab.id, {action: 'toggle'});
                    } catch (error) {
                        // 忽略无法发送消息的标签页
                    }
                }
            }
        } catch (error) {
            console.error('切换所有标签页时出错:', error);
        }
    }
    
    async getGlobalStatus() {
        try {
            const tabs = await chrome.tabs.query({});
            let totalClosed = 0;
            let enabledTabs = 0;
            let totalTabs = 0;
            
            for (const tab of tabs) {
                if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                    totalTabs++;
                    try {
                        const response = await chrome.tabs.sendMessage(tab.id, {action: 'getStatus'});
                        if (response) {
                            if (response.enabled) enabledTabs++;
                            totalClosed += response.closedCount || 0;
                        }
                    } catch (error) {
                        // 忽略无法获取状态的标签页
                    }
                }
            }
            
            return {
                totalTabs,
                enabledTabs,
                totalClosed,
                globalEnabled: enabledTabs > 0
            };
        } catch (error) {
            console.error('获取全局状态时出错:', error);
            return {
                totalTabs: 0,
                enabledTabs: 0,
                totalClosed: 0,
                globalEnabled: false
            };
        }
    }
    
    async getTabStatus(tabId) {
        try {
            const response = await chrome.tabs.sendMessage(tabId, {action: 'getStatus'});
            return response || {enabled: false, closedCount: 0};
        } catch (error) {
            return {enabled: false, closedCount: 0};
        }
    }
    
    showWelcomeNotification() {
        // 可以在这里添加欢迎通知逻辑
        console.log('欢迎使用广告弹窗杀手！');
    }
}

// 创建后台服务实例
const backgroundService = new BackgroundService();
