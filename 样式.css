/* 广告弹窗杀手 - 样式文件 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  line-height: 1.6;
}

.container {
  width: 350px;
  min-height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 5px;
}

.subtitle {
  font-size: 12px;
  opacity: 0.9;
}

.main {
  padding: 20px;
}

.status-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 14px;
  color: #666;
}

.status-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
}

.btn-primary.disabled {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.btn-primary.disabled:hover {
  background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.info-section,
.settings-section {
  margin-bottom: 20px;
}

.info-section h3,
.settings-section h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 2px solid #eee;
}

.info-list {
  list-style: none;
  padding-left: 0;
}

.info-list li {
  font-size: 13px;
  color: #666;
  margin-bottom: 5px;
  padding-left: 15px;
  position: relative;
}

.info-list li::before {
  content: '•';
  color: #ff6b6b;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.setting-item {
  margin-bottom: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 3px;
  margin-right: 10px;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #4CAF50;
  border-color: #4CAF50;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.footer {
  background: #f8f9fa;
  padding: 15px 20px;
  text-align: center;
  border-top: 1px solid #eee;
}

.footer p {
  font-size: 12px;
  color: #666;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-indicator.active {
  background: #4CAF50;
  box-shadow: 0 0 6px rgba(76, 175, 80, 0.6);
}

.status-indicator.inactive {
  background: #f44336;
  box-shadow: 0 0 6px rgba(244, 67, 54, 0.6);
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .container {
    width: 100%;
    min-height: 100vh;
    border-radius: 0;
  }
}
