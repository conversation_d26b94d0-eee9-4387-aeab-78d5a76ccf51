/* 弹窗杀手 - 现代化样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: #333;
  line-height: 1.5;
  overflow: hidden;
}

.app {
  width: 320px;
  height: 480px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 状态栏 */
.status-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
}

.status-indicator {
  position: relative;
  width: 12px;
  height: 12px;
}

.pulse-dot {
  width: 12px;
  height: 12px;
  background: #4ade80;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.pulse-dot.inactive {
  background: #f87171;
  animation: none;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1px;
  background: #e5e7eb;
  margin: 0;
}

.stat-card {
  background: white;
  padding: 20px 12px;
  text-align: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  background: #f9fafb;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 控制区域 */
.control-section {
  padding: 24px 20px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn {
  width: 120px;
  height: 120px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.toggle-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4);
}

.toggle-btn:active {
  transform: scale(0.95);
}

.toggle-btn.disabled {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.toggle-btn.disabled:hover {
  box-shadow: 0 12px 35px rgba(239, 68, 68, 0.4);
}

.btn-icon {
  font-size: 32px;
  line-height: 1;
}

.toggle-btn span {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 1px;
  background: #e5e7eb;
  margin: 0;
}

.action-btn {
  flex: 1;
  background: white;
  border: none;
  padding: 12px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: #6b7280;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn span {
  font-size: 16px;
}

/* 设置面板 */
.settings-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.settings-panel.show {
  transform: translateY(0);
}

.panel-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 设置组 */
.setting-group {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.setting-group:last-child {
  border-bottom: none;
}

.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
  flex-shrink: 0;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e1;
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
  background-color: #10b981;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.setting-info {
  flex: 1;
}

.setting-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.setting-desc {
  font-size: 12px;
  color: #6b7280;
}

/* 底部信息 */
.footer-info {
  background: #f9fafb;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f3f4f6;
  margin-top: auto;
}

.version {
  font-size: 11px;
  color: #9ca3af;
  font-weight: 500;
}

.target-info {
  font-size: 10px;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 8px;
  border-radius: 12px;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes countUp {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
    color: #10b981;
  }
  100% {
    transform: scale(1);
  }
}

.count-animation {
  animation: countUp 0.5s ease;
}
