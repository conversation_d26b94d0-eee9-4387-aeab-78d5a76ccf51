// 广告弹窗杀手 - 内容脚本
class AdPopupKiller {
    constructor() {
        this.isEnabled = true;
        this.scanInterval = null;
        this.closedCount = 0;
        
        // 只针对你提供的特定弹窗的关闭按钮选择器
        this.closeButtonSelectors = [
            // 你提供的特定SVG元素
            'svg[data-testid="beast-core-modal-icon-close"]',
            // 对应的CSS类名
            '.MDL_headerCloseIcon_5-118-0',
            '.ICN_outerWrapper_5-118-0',
            '.ICN_svgIcon_5-118-0'
        ];
        
        // 特定弹窗容器选择器（更严格的匹配）
        this.popupContainerSelectors = [
            // 只匹配包含特定类名模式的容器
            '[class*="MDL_"]',
            '[class*="modal"]',
            '[role="dialog"]',
            '[role="alertdialog"]'
        ];
        
        this.init();
    }
    
    init() {
        console.log('广告弹窗杀手已启动');
        this.startScanning();
        
        // 监听来自后台脚本的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'toggle') {
                this.toggle();
                sendResponse({enabled: this.isEnabled});
            } else if (request.action === 'getStatus') {
                sendResponse({
                    enabled: this.isEnabled,
                    closedCount: this.closedCount
                });
            }
        });
    }
    
    startScanning() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
        
        this.scanInterval = setInterval(() => {
            if (this.isEnabled) {
                this.scanAndClosePopups();
            }
        }, 500); // 每0.5秒扫描一次
    }
    
    scanAndClosePopups() {
        try {
            // 查找并关闭弹窗
            const closeButtons = this.findCloseButtons();
            
            closeButtons.forEach(button => {
                if (this.isVisibleAndClickable(button)) {
                    this.clickCloseButton(button);
                }
            });
            
        } catch (error) {
            console.error('扫描弹窗时出错:', error);
        }
    }
    
    findCloseButtons() {
        const buttons = [];

        // 使用特定选择器查找关闭按钮
        this.closeButtonSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // 额外验证：确保是你提供的特定SVG元素
                    if (this.isTargetCloseButton(element) && !buttons.includes(element)) {
                        buttons.push(element);
                    }
                });
            } catch (e) {
                // 忽略无效的选择器
            }
        });

        return buttons;
    }

    isTargetCloseButton(element) {
        // 验证是否是目标关闭按钮
        if (element.tagName === 'svg') {
            // 检查SVG元素的特征
            const testId = element.getAttribute('data-testid');
            const viewBox = element.getAttribute('viewBox');
            const classes = element.className.baseVal || element.className;

            // 必须匹配你提供的SVG特征
            return testId === 'beast-core-modal-icon-close' &&
                   viewBox === '0 0 1024 1024' &&
                   (classes.includes('ICN_outerWrapper_5-118-0') ||
                    classes.includes('MDL_headerCloseIcon_5-118-0') ||
                    classes.includes('ICN_svgIcon_5-118-0'));
        }

        // 如果是其他元素，检查是否包含目标SVG
        const targetSvg = element.querySelector('svg[data-testid="beast-core-modal-icon-close"]');
        return targetSvg !== null;
    }
    
    isVisibleAndClickable(element) {
        if (!element) return false;
        
        // 检查元素是否可见
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || 
            style.visibility === 'hidden' || 
            style.opacity === '0') {
            return false;
        }
        
        // 检查元素是否在视口内
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
            return false;
        }
        
        // 检查是否在弹窗容器内
        return this.isInPopupContainer(element);
    }
    
    isInPopupContainer(element) {
        // 检查元素是否在特定的弹窗容器内
        let parent = element.parentElement;
        while (parent) {
            const className = parent.className || '';

            // 更严格的检查：必须包含特定的类名模式
            if (className.includes && (
                className.includes('MDL_') ||
                className.includes('modal') ||
                parent.getAttribute('role') === 'dialog' ||
                parent.getAttribute('role') === 'alertdialog'
            )) {
                // 额外检查：确保容器有较高的z-index（弹窗特征）
                const style = window.getComputedStyle(parent);
                const zIndex = parseInt(style.zIndex);
                if (zIndex > 100) {
                    return true;
                }
            }

            parent = parent.parentElement;
        }

        return false;
    }
    
    clickCloseButton(button) {
        try {
            console.log('发现目标弹窗关闭按钮，正在点击...', button);

            // 记录关闭的弹窗信息（用于调试）
            const testId = button.getAttribute('data-testid');
            const classes = button.className.baseVal || button.className;
            console.log('关闭按钮详情:', { testId, classes });

            // 尝试多种点击方式
            if (button.click) {
                button.click();
            } else {
                // 创建点击事件
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                button.dispatchEvent(clickEvent);
            }

            this.closedCount++;
            console.log(`已关闭 ${this.closedCount} 个目标弹窗`);

        } catch (error) {
            console.error('点击关闭按钮时出错:', error);
        }
    }
    
    toggle() {
        this.isEnabled = !this.isEnabled;
        console.log(`广告弹窗杀手已${this.isEnabled ? '启用' : '禁用'}`);
        
        if (this.isEnabled) {
            this.startScanning();
        } else if (this.scanInterval) {
            clearInterval(this.scanInterval);
            this.scanInterval = null;
        }
    }
    
    destroy() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
    }
}

// 创建实例
const adPopupKiller = new AdPopupKiller();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    adPopupKiller.destroy();
});
