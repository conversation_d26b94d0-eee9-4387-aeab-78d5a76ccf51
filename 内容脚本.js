// 广告弹窗杀手 - 内容脚本
class AdPopupKiller {
    constructor() {
        this.isEnabled = true;
        this.scanInterval = null;
        this.closedCount = 0;
        
        // 常见的关闭按钮选择器
        this.closeButtonSelectors = [
            // 基于你提供的SVG元素
            'svg[data-testid="beast-core-modal-icon-close"]',
            '.MDL_headerCloseIcon_5-118-0',
            
            // 通用关闭按钮选择器
            '[class*="close"]',
            '[class*="Close"]',
            '[class*="CLOSE"]',
            '[id*="close"]',
            '[id*="Close"]',
            '[data-dismiss="modal"]',
            '[aria-label*="close"]',
            '[aria-label*="Close"]',
            '[aria-label*="关闭"]',
            '[title*="close"]',
            '[title*="Close"]',
            '[title*="关闭"]',
            
            // 模态框和弹窗相关
            '.modal-close',
            '.popup-close',
            '.dialog-close',
            '.overlay-close',
            '.lightbox-close',
            
            // 常见的X按钮
            'button[class*="x"]',
            'span[class*="x"]',
            'div[class*="x"]',
            
            // 广告相关
            '.ad-close',
            '.advertisement-close',
            '[class*="ad-close"]',
            '[class*="banner-close"]'
        ];
        
        // 弹窗容器选择器
        this.popupContainerSelectors = [
            '.modal',
            '.popup',
            '.dialog',
            '.overlay',
            '.lightbox',
            '[class*="modal"]',
            '[class*="popup"]',
            '[class*="dialog"]',
            '[class*="overlay"]',
            '[role="dialog"]',
            '[role="alertdialog"]'
        ];
        
        this.init();
    }
    
    init() {
        console.log('广告弹窗杀手已启动');
        this.startScanning();
        
        // 监听来自后台脚本的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'toggle') {
                this.toggle();
                sendResponse({enabled: this.isEnabled});
            } else if (request.action === 'getStatus') {
                sendResponse({
                    enabled: this.isEnabled,
                    closedCount: this.closedCount
                });
            }
        });
    }
    
    startScanning() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
        
        this.scanInterval = setInterval(() => {
            if (this.isEnabled) {
                this.scanAndClosePopups();
            }
        }, 500); // 每0.5秒扫描一次
    }
    
    scanAndClosePopups() {
        try {
            // 查找并关闭弹窗
            const closeButtons = this.findCloseButtons();
            
            closeButtons.forEach(button => {
                if (this.isVisibleAndClickable(button)) {
                    this.clickCloseButton(button);
                }
            });
            
        } catch (error) {
            console.error('扫描弹窗时出错:', error);
        }
    }
    
    findCloseButtons() {
        const buttons = [];
        
        // 使用所有选择器查找关闭按钮
        this.closeButtonSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (!buttons.includes(element)) {
                        buttons.push(element);
                    }
                });
            } catch (e) {
                // 忽略无效的选择器
            }
        });
        
        return buttons;
    }
    
    isVisibleAndClickable(element) {
        if (!element) return false;
        
        // 检查元素是否可见
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || 
            style.visibility === 'hidden' || 
            style.opacity === '0') {
            return false;
        }
        
        // 检查元素是否在视口内
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
            return false;
        }
        
        // 检查是否在弹窗容器内
        return this.isInPopupContainer(element);
    }
    
    isInPopupContainer(element) {
        // 检查元素是否在弹窗容器内
        let parent = element.parentElement;
        while (parent) {
            const className = parent.className || '';
            const id = parent.id || '';
            
            // 检查是否匹配弹窗容器选择器
            for (let selector of this.popupContainerSelectors) {
                try {
                    if (parent.matches(selector)) {
                        return true;
                    }
                } catch (e) {
                    // 忽略无效选择器
                }
            }
            
            // 检查z-index是否很高（通常弹窗有高z-index）
            const style = window.getComputedStyle(parent);
            const zIndex = parseInt(style.zIndex);
            if (zIndex > 1000) {
                return true;
            }
            
            parent = parent.parentElement;
        }
        
        return false;
    }
    
    clickCloseButton(button) {
        try {
            console.log('发现弹窗关闭按钮，正在点击...', button);
            
            // 尝试多种点击方式
            if (button.click) {
                button.click();
            } else {
                // 创建点击事件
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                button.dispatchEvent(clickEvent);
            }
            
            this.closedCount++;
            console.log(`已关闭 ${this.closedCount} 个弹窗`);
            
        } catch (error) {
            console.error('点击关闭按钮时出错:', error);
        }
    }
    
    toggle() {
        this.isEnabled = !this.isEnabled;
        console.log(`广告弹窗杀手已${this.isEnabled ? '启用' : '禁用'}`);
        
        if (this.isEnabled) {
            this.startScanning();
        } else if (this.scanInterval) {
            clearInterval(this.scanInterval);
            this.scanInterval = null;
        }
    }
    
    destroy() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
    }
}

// 创建实例
const adPopupKiller = new AdPopupKiller();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    adPopupKiller.destroy();
});
