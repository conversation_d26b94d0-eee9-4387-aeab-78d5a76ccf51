# 广告弹窗杀手 - Chrome浏览器插件

## 功能特点

- ✅ 自动检测网页广告弹窗
- ✅ 每0.5秒扫描一次页面
- ✅ 智能识别关闭按钮并自动点击
- ✅ 支持大多数常见弹窗类型
- ✅ 可随时启用/禁用功能
- ✅ 实时统计已关闭弹窗数量
- ✅ 美观的用户界面

## 安装步骤

### 1. 准备插件文件
确保你有以下文件：
- `manifest.json` - 插件配置文件
- `内容脚本.js` - 主要功能脚本
- `后台脚本.js` - 后台服务脚本
- `弹出页面.html` - 插件界面
- `弹出脚本.js` - 界面交互脚本
- `样式.css` - 界面样式文件

### 2. 在Chrome中安装插件

1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 在右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹
6. 插件安装完成！

### 3. 使用插件

1. 安装后，浏览器工具栏会出现插件图标
2. 点击图标打开控制面板
3. 点击"启用"按钮开始自动关闭弹窗
4. 插件会在后台自动工作，无需手动操作

## 工作原理

### 扫描机制
- 每0.5秒扫描一次页面
- 查找常见的关闭按钮选择器
- 检测弹窗容器和高z-index元素

### 识别规则
插件会识别以下类型的关闭按钮：
- 包含"close"、"关闭"等关键词的元素
- 具有特定CSS类名的按钮
- 模态框和弹窗的关闭图标
- X形状的关闭按钮

### 安全机制
- 只在弹窗容器内查找关闭按钮
- 检查元素可见性和可点击性
- 避免误点击正常页面元素

## 支持的弹窗类型

- 广告弹窗
- 模态对话框
- 覆盖层弹窗
- 灯箱效果弹窗
- 通知弹窗
- 订阅提示框

## 设置选项

- **新标签页自动启用**: 在新打开的标签页中自动启用插件
- **显示关闭通知**: 关闭弹窗时显示通知（可选）

## 注意事项

1. 插件只在普通网页上工作，不支持Chrome内部页面
2. 某些网站可能有特殊的弹窗实现，需要时间适配
3. 如果误关闭了重要弹窗，可以临时禁用插件
4. 建议在重要操作前暂时禁用插件

## 故障排除

### 插件不工作
1. 检查是否已启用插件
2. 刷新页面重新加载脚本
3. 检查浏览器控制台是否有错误信息

### 无法关闭某些弹窗
1. 该弹窗可能使用了特殊的实现方式
2. 可以手动添加新的选择器规则
3. 联系开发者反馈问题

### 误关闭正常弹窗
1. 临时禁用插件
2. 调整识别规则的严格程度
3. 在设置中关闭自动启用

## 技术细节

- **Manifest版本**: V3
- **权限要求**: activeTab, scripting, storage
- **支持页面**: 所有HTTP/HTTPS网页
- **更新频率**: 500毫秒扫描间隔

## 版本信息

- **当前版本**: 1.0
- **发布日期**: 2024年
- **兼容性**: Chrome 88+

## 开发者信息

如需定制或有问题反馈，请联系开发者。

---

**免责声明**: 本插件仅用于改善浏览体验，请合理使用。某些网站的弹窗可能包含重要信息，请谨慎操作。
