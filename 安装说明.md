# 广告弹窗杀手 - Chrome浏览器插件

## 功能特点

- ✅ **精确定位**：只关闭特定的目标弹窗
- ✅ 每0.5秒扫描一次页面
- ✅ 智能识别特定关闭按钮并自动点击
- ✅ **安全机制**：不会误关闭其他弹窗或对话框
- ✅ 可随时启用/禁用功能
- ✅ 实时统计已关闭弹窗数量
- ✅ **现代化界面**：全新设计的美观用户界面
- ✅ **动画效果**：流畅的交互动画和视觉反馈

## 安装步骤

### 1. 准备插件文件
确保你有以下文件：
- `manifest.json` - 插件配置文件
- `内容脚本.js` - 主要功能脚本
- `后台脚本.js` - 后台服务脚本
- `弹出页面.html` - 插件界面
- `弹出脚本.js` - 界面交互脚本
- `样式.css` - 界面样式文件

### 2. 在Chrome中安装插件

1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 在右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹
6. 插件安装完成！

### 3. 使用插件

1. 安装后，浏览器工具栏会出现插件图标
2. 点击图标打开现代化控制面板
3. 点击中央的"启动拦截"按钮开始自动关闭弹窗
4. 插件会在后台自动工作，实时显示拦截统计
5. 可以使用"清零"按钮重置计数器
6. 点击"设置"按钮可以调整插件选项

## 工作原理

### 扫描机制
- 每0.5秒扫描一次页面
- 查找常见的关闭按钮选择器
- 检测弹窗容器和高z-index元素

### 识别规则
插件**只会**识别以下特定的关闭按钮：
- `svg[data-testid="beast-core-modal-icon-close"]` - 你提供的特定SVG元素
- 包含类名 `MDL_headerCloseIcon_5-118-0` 的元素
- 包含类名 `ICN_outerWrapper_5-118-0` 的元素
- 包含类名 `ICN_svgIcon_5-118-0` 的元素

**重要**：插件不会关闭其他类型的弹窗或对话框，确保安全性。

### 安全机制
- 只在弹窗容器内查找关闭按钮
- 检查元素可见性和可点击性
- 避免误点击正常页面元素

## 支持的弹窗类型

**仅支持你指定的特定弹窗类型**：
- 包含 `data-testid="beast-core-modal-icon-close"` 的SVG关闭按钮
- 具有特定CSS类名模式的弹窗（MDL_、ICN_前缀）
- 必须在模态框容器内（role="dialog" 或包含特定类名）

**不支持**：
- 普通网站的对话框
- 浏览器原生弹窗
- 其他类型的广告弹窗

## 设置选项

- **新标签页自动启用**: 在新打开的标签页中自动启用插件
- **显示关闭通知**: 关闭弹窗时显示通知（可选）

## 注意事项

1. 插件只在普通网页上工作，不支持Chrome内部页面
2. 某些网站可能有特殊的弹窗实现，需要时间适配
3. 如果误关闭了重要弹窗，可以临时禁用插件
4. 建议在重要操作前暂时禁用插件

## 故障排除

### 插件不工作
1. 检查是否已启用插件
2. 刷新页面重新加载脚本
3. 检查浏览器控制台是否有错误信息

### 无法关闭某些弹窗
1. 该弹窗可能使用了特殊的实现方式
2. 可以手动添加新的选择器规则
3. 联系开发者反馈问题

### 误关闭正常弹窗
1. 临时禁用插件
2. 调整识别规则的严格程度
3. 在设置中关闭自动启用

## 技术细节

- **Manifest版本**: V3
- **权限要求**: activeTab, scripting, storage
- **支持页面**: 所有HTTP/HTTPS网页
- **更新频率**: 500毫秒扫描间隔

## 版本信息

- **当前版本**: 1.0
- **发布日期**: 2024年
- **兼容性**: Chrome 88+

## 开发者信息

如需定制或有问题反馈，请联系开发者。

---

**免责声明**: 本插件仅用于改善浏览体验，请合理使用。某些网站的弹窗可能包含重要信息，请谨慎操作。
